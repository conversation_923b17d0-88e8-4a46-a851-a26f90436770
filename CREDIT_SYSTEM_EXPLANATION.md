# 统一积分系统详细说明

## 🎯 统一积分扣减逻辑

### 新的统一扣减机制

每次调用图片生成 API 时，系统会：

1. **检查用户积分**：获取所有有效积分记录
2. **验证积分充足**：确保总积分 >= 需要扣减的积分
3. **统一扣减**：使用最早过期的积分记录的订单号
4. **创建单一记录**：每次扣减只创建一条记录

### 统一积分系统特点

#### ✅ 所有积分都有订单号
```
- 付费积分：order_no = "ord_ABC123" (真实订单)
- 新用户积分：order_no = "ord_new_user_12345678" (虚拟订单)
- 系统赠送：order_no = "ord_system_add_123" (虚拟订单)
- 每日奖励：order_no = "ord_daily_reward_456" (虚拟订单)
```

#### ✅ 每次扣减只创建一条记录
```
用户积分情况：
- 新用户积分：10 积分 (order_no: ord_new_user_12345678)
- 付费积分：5 积分 (order_no: ord_ABC123)

当用户生成图片需要 3 积分时：
- 使用最早过期的积分记录的订单号
- 创建 1 条扣减记录：credits = -3, order_no = "ord_new_user_12345678"
```

## 📊 积分记录表结构

```sql
CREATE TABLE credits (
    id SERIAL PRIMARY KEY,
    trans_no VARCHAR(255) UNIQUE NOT NULL,    -- 交易号
    created_at timestamptz,                   -- 创建时间
    user_uuid VARCHAR(255) NOT NULL,          -- 用户ID
    trans_type VARCHAR(50) NOT NULL,          -- 交易类型
    credits INT NOT NULL,                     -- 积分数量（正数=增加，负数=扣减）
    order_no VARCHAR(255),                    -- 关联订单号
    expired_at timestamptz                    -- 过期时间
);
```

## 🔍 积分记录类型

### 增加积分记录 (credits > 0)
- `new_user`: 新用户赠送积分
- `order_pay`: 用户购买积分
- `system_add`: 系统赠送积分
- `daily_reward`: 每日签到积分

### 扣减积分记录 (credits < 0)
- `system_add`: 图片生成扣减
- `ping`: API 调用扣减

## 📋 查询示例

### 1. 查看用户当前积分余额
```sql
SELECT SUM(credits) as current_balance
FROM credits
WHERE user_uuid = 'your-user-uuid'
AND expired_at > NOW();
```

### 2. 查看用户积分使用历史
```sql
SELECT 
    created_at,
    trans_type,
    credits,
    order_no,
    CASE 
        WHEN credits > 0 THEN '充值'
        ELSE '消费'
    END as action_type
FROM credits
WHERE user_uuid = 'your-user-uuid'
ORDER BY created_at DESC;
```

### 3. 按订单分组查看积分情况
```sql
SELECT 
    CASE 
        WHEN order_no = '' THEN '免费积分'
        ELSE order_no
    END as source,
    SUM(credits) as total_credits,
    COUNT(*) as record_count
FROM credits
WHERE user_uuid = 'your-user-uuid'
GROUP BY order_no
ORDER BY total_credits DESC;
```

### 4. 查看最近的积分变动
```sql
SELECT 
    created_at,
    trans_type,
    credits,
    CASE 
        WHEN order_no = '' THEN '免费积分'
        ELSE order_no
    END as source
FROM credits
WHERE user_uuid = 'your-user-uuid'
AND created_at > NOW() - INTERVAL '24 hours'
ORDER BY created_at DESC;
```

## 🎯 积分扣减优先级

1. **按过期时间排序**：优先使用快过期的积分
2. **按积分来源**：
   - 免费积分 (order_no = "")
   - 付费积分 (order_no = "ord_xxx")

## 🔧 调试积分问题

### 1. 检查用户总积分
```bash
node debug/check-user-credits.js
```

### 2. 测试积分扣减
```bash
node debug/test-credit-deduction.js
```

### 3. 查看详细日志
启动开发服务器后，观察控制台输出：
- `💰 开始扣减积分`
- `📊 用户有效积分记录数量`
- `📝 从订单 xxx 扣减 xxx 积分`
- `✅ 创建扣减记录`

## ❓ 常见问题

### Q: 为什么一次生成图片会有多条扣减记录？
A: 这是正常的！当用户的积分来自多个来源时，系统会为每个来源创建对应的扣减记录。

### Q: order_no 的格式说明？
A:
- `ord_ABC123`: 真实付费订单
- `ord_new_user_xxx`: 新用户赠送积分
- `ord_system_xxx`: 系统赠送积分
- `ord_daily_xxx`: 每日奖励积分

### Q: 如何查看用户的积分余额？
A: 使用 `SUM(credits)` 查询所有未过期的积分记录。

### Q: 积分扣减的顺序是什么？
A: 按过期时间排序，优先使用快过期的积分（FIFO 原则）。

## 🚀 优化建议

1. **定期清理过期积分记录**
2. **添加积分使用统计**
3. **实现积分转赠功能**
4. **添加积分使用提醒**
