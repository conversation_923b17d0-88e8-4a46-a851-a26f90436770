import { Metadata } from "next"
import { getTranslations } from "next-intl/server"
import ImageGenerator from "@/components/pollinations/ImageGenerator"
import DashboardClient from "@/components/dash/DashboardClient"

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("pages.landing")
  return {
    title: "AI图片生成 - 从提示词生成图片",
    description: "使用AI提示词生成高质量图片",
  }
}

export default async function ImageGeneratorPage() {
  return (
    <DashboardClient>
      <div className="flex min-h-screen">
        {/* 主内容区 */}
        <div className="flex-1 overflow-y-auto">
          <div className="bg-gradient-to-b from-gray-900 to-black min-h-screen">
            <div className="container mx-auto px-6 py-8">
              <div className="max-w-7xl mx-auto">
                <div className="text-center mb-8">
                  <h1 className="text-4xl font-bold text-white mb-4">AI图片生成</h1>
                  <p className="text-gray-400 text-lg">从提示词库中选择并生成高质量AI图片</p>
                </div>
                <ImageGenerator />
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardClient>
  )
} 