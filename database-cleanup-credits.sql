-- 数据库清理脚本：为现有的空订单号积分添加虚拟订单号
-- 请在 Supabase 控制台的 SQL Editor 中运行此脚本

-- 1. 查看当前空订单号的积分记录
SELECT 
    id, 
    user_uuid, 
    trans_type, 
    credits, 
    order_no, 
    created_at,
    expired_at
FROM credits 
WHERE order_no = '' OR order_no IS NULL
ORDER BY created_at DESC;

-- 2. 为新用户积分创建虚拟订单号
UPDATE credits 
SET order_no = CONCAT('ord_new_user_', SUBSTRING(user_uuid, -8))
WHERE (order_no = '' OR order_no IS NULL) 
AND trans_type = 'new_user'
AND credits > 0;

-- 3. 为系统赠送积分创建虚拟订单号
UPDATE credits 
SET order_no = CONCAT('ord_system_add_', id)
WHERE (order_no = '' OR order_no IS NULL) 
AND trans_type = 'system_add'
AND credits > 0;

-- 4. 为每日奖励积分创建虚拟订单号
UPDATE credits 
SET order_no = CONCAT('ord_daily_reward_', id)
WHERE (order_no = '' OR order_no IS NULL) 
AND trans_type = 'daily_reward'
AND credits > 0;

-- 5. 为其他类型的积分创建虚拟订单号
UPDATE credits 
SET order_no = CONCAT('ord_', trans_type, '_', id)
WHERE (order_no = '' OR order_no IS NULL) 
AND credits > 0;

-- 6. 为扣减记录使用对应的充值记录订单号
-- 这个比较复杂，需要根据用户和时间来匹配
WITH user_credits AS (
    SELECT 
        user_uuid,
        order_no,
        expired_at,
        ROW_NUMBER() OVER (PARTITION BY user_uuid ORDER BY expired_at ASC) as rn
    FROM credits 
    WHERE credits > 0 
    AND order_no != ''
)
UPDATE credits 
SET order_no = (
    SELECT uc.order_no 
    FROM user_credits uc 
    WHERE uc.user_uuid = credits.user_uuid 
    AND uc.rn = 1
)
WHERE (order_no = '' OR order_no IS NULL) 
AND credits < 0;

-- 7. 验证清理结果
SELECT 
    'After cleanup' as status,
    COUNT(*) as total_records,
    COUNT(CASE WHEN order_no = '' OR order_no IS NULL THEN 1 END) as empty_order_no,
    COUNT(CASE WHEN order_no != '' AND order_no IS NOT NULL THEN 1 END) as with_order_no
FROM credits;

-- 8. 按订单号分组查看积分情况
SELECT 
    CASE 
        WHEN order_no LIKE 'ord_new_user_%' THEN '新用户积分'
        WHEN order_no LIKE 'ord_system_%' THEN '系统积分'
        WHEN order_no LIKE 'ord_daily_%' THEN '每日奖励'
        WHEN order_no LIKE 'ord_%' AND order_no NOT LIKE 'ord_new_user_%' AND order_no NOT LIKE 'ord_system_%' THEN '付费积分'
        ELSE '其他'
    END as credit_type,
    COUNT(*) as record_count,
    SUM(credits) as total_credits
FROM credits
GROUP BY 
    CASE 
        WHEN order_no LIKE 'ord_new_user_%' THEN '新用户积分'
        WHEN order_no LIKE 'ord_system_%' THEN '系统积分'
        WHEN order_no LIKE 'ord_daily_%' THEN '每日奖励'
        WHEN order_no LIKE 'ord_%' AND order_no NOT LIKE 'ord_new_user_%' AND order_no NOT LIKE 'ord_system_%' THEN '付费积分'
        ELSE '其他'
    END
ORDER BY total_credits DESC;
