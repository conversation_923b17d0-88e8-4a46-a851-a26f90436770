import {
  findCreditByOrderNo,
  getUserValidCredits,
  insertCredit,
} from "@/models/credit";

import { Credit } from "@/types/credit";
import { Order } from "@/types/order";
import { UserCredits } from "@/types/user";
import { findUserByUuid } from "@/models/user";
import { getFirstPaidOrderByUserUuid } from "@/models/order";
import { getIsoTimestr } from "@/lib/time";
import { getSnowId } from "@/lib/hash";
import { supabase } from "@/lib/supabase";

export enum CreditsTransType {
  NewUser = "new_user", // initial credits for new user
  OrderPay = "order_pay", // user pay for credits
  SystemAdd = "system_add", // system add credits
  Ping = "ping", // cost for ping api
  SystemRefund = "system_refund", // system refund credits
  DailyReward = "daily_reward", // daily reward credits
}

export enum CreditsAmount {
  NewUserGet = 10,
  PingCost = 1,
  ImageGenerationCost = 3,
  DailyReward = 10, // 每日赠送积分数量
}

export async function getUserCredits(user_uuid: string): Promise<UserCredits> {
  let user_credits: UserCredits = {
    left_credits: 0,
  };

  try {
    const first_paid_order = await getFirstPaidOrderByUserUuid(user_uuid);
    if (first_paid_order) {
      user_credits.is_recharged = true;
    }

    const credits = await getUserValidCredits(user_uuid);
    if (credits) {
      credits.forEach((v: Credit) => {
        user_credits.left_credits += v.credits;
      });
    }

    if (user_credits.left_credits < 0) {
      user_credits.left_credits = 0;
    }

    if (user_credits.left_credits > 0) {
      user_credits.is_pro = true;
    }

    return user_credits;
  } catch (e) {
    console.log("get user credits failed: ", e);
    return user_credits;
  }
}

export async function decreaseCredits({
  user_uuid,
  trans_type,
  credits,
}: {
  user_uuid: string;
  trans_type: CreditsTransType;
  credits: number;
}) {
  try {
    console.log(`💰 开始扣减积分: ${credits} 积分，用户: ${user_uuid}, 类型: ${trans_type}`);

    // 1. 获取用户所有有效积分记录（按过期时间排序，优先使用快过期的）
    const userCredits = await getUserValidCredits(user_uuid);
    console.log(`📊 用户有效积分记录数量: ${userCredits?.length || 0}`);

    if (!userCredits || userCredits.length === 0) {
      console.log(`❌ 用户没有有效积分`);
      throw new Error(`用户没有有效积分`);
    }

    // 2. 计算总可用积分
    let totalAvailableCredits = 0;
    userCredits.forEach((credit: Credit) => {
      totalAvailableCredits += credit.credits;
    });

    console.log(`📊 用户总可用积分: ${totalAvailableCredits}`);

    // 3. 检查积分是否足够
    if (totalAvailableCredits < credits) {
      console.log(`❌ 积分不足: 需要 ${credits}，可用 ${totalAvailableCredits}`);
      throw new Error(`积分不足，需要 ${credits} 积分，当前可用 ${totalAvailableCredits} 积分`);
    }

    // 4. 统一扣减逻辑：使用最早过期的积分记录的订单号
    const firstCredit = userCredits[0]; // 最早过期的积分记录
    const order_no = firstCredit.order_no;
    const expired_at = firstCredit.expired_at;

    console.log(`📝 统一从订单 ${order_no} 扣减 ${credits} 积分`);

    // 5. 创建单一扣减记录
    const new_credit: Credit = {
      trans_no: getSnowId(),
      created_at: getIsoTimestr(),
      user_uuid: user_uuid,
      trans_type: trans_type,
      credits: 0 - credits, // 负数表示扣减
      order_no: order_no,
      expired_at: expired_at || "",
    };

    await insertCredit(new_credit);
    console.log(`✅ 创建扣减记录: ${credits} 积分，订单: ${order_no}`);
    console.log(`✅ 积分扣减完成: 总共扣减 ${credits} 积分，创建了 1 条记录`);

  } catch (e) {
    console.log("积分扣减失败: ", e);
    throw e;
  }
}

export async function increaseCredits({
  user_uuid,
  trans_type,
  credits,
  expired_at,
  order_no,
}: {
  user_uuid: string;
  trans_type: string;
  credits: number;
  expired_at?: string;
  order_no?: string;
}) {
  try {
    // 确保所有积分都有订单号
    if (!order_no) {
      // 如果没有提供订单号，创建一个系统订单号
      order_no = `ord_system_${trans_type}_${Date.now()}`;
      console.log(`⚠️  为积分创建系统订单号: ${order_no}`);
    }

    const new_credit: Credit = {
      trans_no: getSnowId(),
      created_at: getIsoTimestr(),
      user_uuid: user_uuid,
      trans_type: trans_type,
      credits: credits,
      order_no: order_no,
      expired_at: expired_at || "",
    };

    await insertCredit(new_credit);
    console.log(`✅ 增加积分: ${credits} 积分，订单: ${order_no}`);
  } catch (e) {
    console.log("increase credits failed: ", e);
    throw e;
  }
}

export async function updateCreditForOrder(order: Order) {
  try {
    const credit = await findCreditByOrderNo(order.order_no);
    if (credit) {
      // order already increased credit
      return;
    }

    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: order.credits,
      expired_at: order.expired_at,
      order_no: order.order_no,
    });
  } catch (e) {
    console.log("update credit for order failed: ", e);
    throw e;
  }
}
